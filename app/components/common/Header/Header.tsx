import React, { useEffect, useMemo, useRef, useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { useStore } from '@nanostores/react';
import ClientOnly from '~/components/common/ClientOnly';
import { chatStore,setEstimationsData } from '~/ai/lib/stores/chat';
import { classNames } from '~/utils/classNames';
import { ChatDescription } from '~/ai/lib/persistence/ChatDescription.client';
import userImg from '/images/user3.svg?url';
import '../../styles/userlist.scss';
import ListItem from './ListItem';
import UserIconDark from '~/assets/icons/user-dark.svg?url';
import { TooltipProvider } from '@radix-ui/react-tooltip';
import '../../styles/ainmated-spinner.scss';
import { getDefaultReferral } from '~/ai/lib/stores/referrals';
import { HeaderProps } from '~/components/types/componentTypes';
import { useUser } from '~/ai/lib/context/userContext';
import { CircleDollarSignIcon, DollarSignIcon, House, MessageSquare } from 'lucide-react';
import bielaLogo from '/biela-logo-only.svg?url';
import bielaText from '/biela-text.svg?url';
import '~/components/styles/index.scss';
import TokenRemaining from '~/ai/components/TokensRemaining.client';
import { FaClock, FaCreditCard, FaDollarSign, FaHome, FaSignOutAlt, FaStar, FaUser } from 'react-icons/fa';
import LanguageSelector from '~/components/LanguageSelector';
import { FaUserGroup } from 'react-icons/fa6';
import { backendApiFetch } from '~/ai/lib/backend-api';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { intervalToDuration } from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import LogoutConfirmModal from '~/components/common/LogoutConfirmModal';

interface HeaderProps {
  handleSendMessage?: (...args: any[]) => void;
  isStreaming?: boolean;
  isDashboardPage?: boolean;
  isSettingsPage?: boolean;
  isProfilePage?: boolean;
  chatStarted?: boolean;
}

export function Header({
  handleSendMessage,
  isStreaming,
  isDashboardPage,
  isSettingsPage,
  isProfilePage,
  chatStarted,
}: HeaderProps = {}) {
  const chat = useStore(chatStore);
  const {estimationsProject} = useStore(chatStore);

  const [showSettings, setShowSettings] = useState(false);
  const { isLoggedIn, user, setUser, logout } = useUser();
  const [userData, setUserData] = useState(user);
  const settingsRef = useRef<HTMLDivElement>(null);
  const location = useLocation();
  const [userDataFromAffiliate, setUserDataFromAffiliate] = useState<
    | undefined
    | {
        teamName: string;
        stars: number;
        avatar: string;
      }
  >(undefined);

  const memoizedAvatarUrl = useMemo(() => {
    if (typeof userData?.profilePicture === 'string' && userData?.profilePicture) {
      return userData.profilePicture;
    }

    if (userDataFromAffiliate?.avatar) {
      return userDataFromAffiliate.avatar;
    }

    return userImg;
  }, [userDataFromAffiliate?.avatar, userData?.profilePicture]);

  useEffect(() => {
    if (user) {
      setUserData(user);
    }
    else {
      try {
        const stored = localStorage.getItem('user');
        if (stored) {
          const parsed = JSON.parse(stored);
          setUserData(parsed);
          setUser(parsed);
          fetchUserDataAffiliate();
        }
      } catch (e) {
        console.error('Error parsing local user:', e);
      }
    }
  }, [user]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (settingsRef.current && !settingsRef.current.contains(event.target as Node)) {
        setShowSettings(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);

    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const fetchUserDataAffiliate = async () => {
    try {
      const response = await backendApiFetch(`/affiliate/user-affiliate-information`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response?.ok) {
        const data:any = await response.json()
        setUserDataFromAffiliate(data );
        return data;
      }

      return null;
    } catch (error) {
      console.error('Error fetching user data from affiliate:', error);
      return null;
    }
  };

  useEffect(() => {
    if (isLoggedIn())
      fetchUserDataAffiliate();
  }, []);

  const handleUserIconClick = () => {
    if (!isLoggedIn()) {
      return;
    }

    setShowSettings(!showSettings);
  };

  const copyReferral = async () => {
    const url = await getDefaultReferral().catch();
    await navigator.clipboard.writeText(url).catch();
  };

  const isDashboardPath = location.pathname === '/dashboard';
  const isProfilePath = location.pathname === '/profile';
  const isChatPath = location.pathname === '/' || location.pathname.startsWith('/chat/');
  const isProjectPath = location.pathname.startsWith('/chat/');
  const { t } = useTranslation('translation');

  useEffect(() => {
    if (!location.pathname.startsWith('/chat/')) {
      setEstimationsData(undefined);
    }
  }, [location.pathname]);

  const navigate = useNavigate();
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  const handleLogoutConfirm = async () => {
    setShowLogoutModal(false);

    if (typeof window !== 'undefined') {

      if (typeof useUser === 'function') {
        try {
          await logout();
        } catch {}
      }

      navigate('/login');
    }
  };

  const handleLogoutCancel = () => {
    setShowLogoutModal(false);
  };

  const ListsItems = useMemo(() => [
    {
      title: isDashboardPath
        ? t('affiliateDashboard', 'Affiliate Dashboard')
        : t('userDashboard', 'User Dashboard'),
      icon: <FaHome className="text-green-400" />,
      description: isDashboardPath
        ? t('returnToAffiliateDashboard', 'Return to affiliate dashboard')
        : t('returnToUserDashboard', 'Return to user dashboard'),
      path: isDashboardPath ? 'https://affiliate.biela.dev/' : '/dashboard',
      isExternal: isDashboardPath,
    },
    ...(isProfilePath || isChatPath
      ? [
          {
            title: t('affiliateDashboard', 'Affiliate Dashboard'),
            icon: <FaStar className="text-yellow-400" />,
            description: t('returnToAffiliateDashboard', 'Return to affiliate dashboard'),
            path: 'https://affiliate.biela.dev/',
            isExternal: true,
          },
        ]
      : []),

    ...(!isProfilePath
      ? [
          {
            title: t('myProfile', 'My Profile'),
            icon: <FaUser className="text-blue-400" />,
            description: t('viewAndEditYourProfile', 'View and edit your profile'),
            path: '/profile',
          },
        ]
      : []),
    {
      title: t('billing', 'Billing'),
      icon: <FaCreditCard className="text-purple-400" />,
      description: t('manageYourBillingInformation', 'Manage your billing information'),
      path: '/settings',
    },
    /*
     * {
     *   title: 'Help',
     *   icon: <FaQuestionCircle className="text-purple-400" />,
     *   description: 'Get support and documentation',
     *   path: '/help',
     * },
     * {
     *   title: 'Settings',
     *   icon: <FaCog className="text-yellow-400" />,
     *   description: 'Manage your preferences',
     *   path: '/settings',
     * },s
     */
    {
      title: t('logout', 'Logout'),
      icon: <FaSignOutAlt className="text-red-400" />,
      description: t('logoutDescription', 'Logout from your account'),
      path: '/logout',
      isExternal: false,
      onClick: isProjectPath
        ? () => setShowLogoutModal(true)
        : undefined,
    },
  ], [
    isDashboardPath,
    isProfilePath,
    isChatPath,
    isProjectPath,
    t,
  ]);


  const redirectingLogo = ()=>{

    if (window.location.pathname.includes('dashboard')){
      window.location.href = '/';
    }
    else if (window.location.pathname === ('/')){
      window.location.href = '/dashboard';
    }
    else {
      window.location.href = '/dashboard';
    }
  }

  const actualTime = useMemo(() => {
    if(estimationsProject) {
      const duration = intervalToDuration({ start: 0, end: estimationsProject.timeMetrics.actual * 1000 });
      const accumulatedDays = (duration.days ?? 0) + (duration.years ?? 0) * 365 + (duration.months ?? 0) * 30;
      const accumulatedDaysString = accumulatedDays > 0 ? `${accumulatedDays}d ` : '';
      const hoursString = (duration.hours ?? 0 > 0) ? `${duration.hours ?? 0}h ` : '';
      const minutesString = `${duration.minutes ?? 0}m `;
      const secondsString = `${duration.seconds ?? 0}s`;
      return `${accumulatedDaysString}${hoursString}${minutesString}${secondsString}`;
    }
    else {
      return '0h';
    }
  }, [estimationsProject?.timeMetrics.actual]);

  return (
    <TooltipProvider>
      {showSettings && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
          onClick={() => setShowSettings(false)}
        />
      )}
      <header
        className={classNames(
          'flex items-center h-[var(--header-height)] justify-between header-index relative z-50 px-4 sm:px-8 py-4 h-20',
          chat.started ? 'border-biela-elements-borderColor' : 'border-b border-[#1F2937AA]',
        )}
      >
        <div
          className="container mx-auto max-sm:!pl-[40px] relative max-sm:px-[25px]"
          style={{ paddingTop: '0', paddingBottom: '0' }}
        >
          <div className="grid grid-cols-2">
            <div className="cursor-pointer flex gap-2 z-10">
              <>
                <a
                  href="/dashboard"
                  onClick={(e) => {
                    e.preventDefault(); // evită redirecționarea default
                    redirectingLogo();  // folosește logica ta
                  }}
                  target="_self"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 sm:gap-3 group"
                >
                  <div
                    className="relative"
                    style={{ transform: 'none', height: '32px', minWidth: '40px' }}
                  >
                    <img
                      src={bielaLogo}
                      alt="BIELA"
                      style={{ position: 'absolute', right: 0, top: -7 }}
                      className="h-[80px] min-w-[80px] max-sm:min-w-[70px] max-sm:h-[70px] z-50"
                    />
                    <div className="absolute inset-0 bg-accent/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300" />
                  </div>
                  <img
                    src={bielaText}
                    alt="biela.dev"
                    className="h-[60px] xl:h-[80px] w-[143px] max-sm:w-[100px]"
                  />
                </a>
              </>
            </div>

            <div className="flex items-center justify-end z-11">
              <div></div>
              {chat.started && !isDashboardPage && !isSettingsPage && !isProfilePage && (
                <span
                  style={{ visibility: 'hidden' }}
                  className=" flex items-center gap-2 flex-1 project-name-container"
                >
                  <span className="px-4 truncate text-center project-name-container-span text-biela-elements-textPrimary max-lg:w-full">
                    <ClientOnly>{() => <ChatDescription />}</ClientOnly>
                  </span>
                </span>
              )}

              <div className={'relative flex items-center gap-3 flex-1 justify-end'}>
                {estimationsProject && (
                  <div className="flex flex-col items-center gap-2 max-[570px]:hidden">
                    <div className="flex flex-nowrap items-center gap-2">
                      {/* Dev Cost */}
                      <div className="flex flex-col min-w-[85px] items-center px-2 py-1 bg-white/[0.02] rounded-md border border-white/[0.05] backdrop-blur-sm hover:bg-white/[0.04] transition-all duration-300">
                        <div className="flex items-center gap-1 mb-0.5">
                          <div className={`w-1.5 h-1.5 rounded-full bg-red-400`}></div>
                          <span className="text-white/60 text-[10px] font-light tracking-wide uppercase ">Dev Cost</span>
                        </div>

                        <div className="flex items-center gap-0.5">
                          <span className="text-red-400 text-xs">$</span>
                          <AnimatePresence mode="wait">
                            <motion.span
                              key={estimationsProject.estimations.estimatedCostTraditional.toLocaleString()}
                              initial={{ y: -8, opacity: 0 }}
                              animate={{ y: 0, opacity: 1 }}
                              exit={{ y: 8, opacity: 0 }}
                              transition={{ duration: 0.3, ease: "easeOut" }}
                              className="text-white font-medium text-sm"
                            >
                              {estimationsProject.estimations.estimatedCostTraditional.toLocaleString()}
                            </motion.span>
                          </AnimatePresence>
                        </div>

                        <div className="flex items-center gap-0.5 mt-0.5">
                          <div className="w-2 h-2 rounded-full bg-white/10 flex items-center justify-center">
                            <div className="w-1 h-1 rounded-full bg-orange-400"></div>
                          </div>

                          <span className="text-white/40 text-[9px] font-light">{estimationsProject.estimations.estimatedTimeTraditional}h</span>
                        </div>
                      </div>

                      {/* Linie embosată */}
                      <div className="w-px h-8 bg-white/10"></div>

                      {/* Biela.dev */}
                      <div className="flex flex-col min-w-[85px] items-center px-2 py-1 bg-white/[0.02] rounded-md border border-white/[0.05] backdrop-blur-sm hover:bg-white/[0.04] transition-all duration-300 ">
                        <div className="flex items-center gap-1 mb-0.5">
                          <div className={`w-1.5 h-1.5 rounded-full bg-[#4ADE80]`}></div>
                          <span className="text-white/60 text-[10px] font-light tracking-wide uppercase">Biela.dev</span>
                        </div>

                        <div className="flex items-center gap-0.5">
                          <span className="text-[#4ADE80] text-xs">$</span>
                          <AnimatePresence mode="wait">
                            <motion.span
                              key={estimationsProject.estimatedCost.tokens.toFixed(2).toLocaleString()}
                              initial={{ y: -8, opacity: 0 }}
                              animate={{ y: 0, opacity: 1 }}
                              exit={{ y: 8, opacity: 0 }}
                              transition={{ duration: 0.3, ease: "easeOut" }}
                              className="text-white font-medium text-sm"
                            >
                              {estimationsProject.estimatedCost.tokens.toFixed(2).toLocaleString()}
                            </motion.span>
                          </AnimatePresence>
                        </div>

                        <div className="flex items-center gap-0.5 mt-0.5">
                          <div className="w-2 h-2 rounded-full bg-white/10 flex items-center justify-center">
                            <div className="w-1 h-1 rounded-full bg-orange-400"></div>
                          </div>

                          <span className="text-white/40 text-[9px] font-light">{actualTime}</span>
                        </div>
                      </div>

                      {/* Linie embosată */}
                      <div className="w-px h-8 bg-white/10"></div>
                      {/* Profit */}
                      <div className="flex flex-col min-w-[85px] items-center px-2 py-1 bg-white/[0.02] rounded-md border border-white/[0.05] backdrop-blur-sm hover:bg-white/[0.04] transition-all duration-300">
                        <div className="flex items-center gap-1 mb-0.5">
                          <div className={`w-1.5 h-1.5 rounded-full bg-[#4ADE80]`}></div>
                          <span className="text-white/60 text-[10px] font-light tracking-wide uppercase">Profit</span>
                        </div>

                        <div className="flex items-center gap-0.5">
                          <span className="text-[#4ADE80] text-xs">$</span>
                          <AnimatePresence mode="wait">
                            <motion.span
                              key={Math.floor(estimationsProject.estimations.estimatedCostTraditional - estimationsProject.estimatedCost.tokens).toLocaleString('en-US').replace(/,/g, ',')}
                              initial={{ y: -8, opacity: 0 }}
                              animate={{ y: 0, opacity: 1 }}
                              exit={{ y: 8, opacity: 0 }}
                              transition={{ duration: 0.3, ease: "easeOut" }}
                              className="text-white font-medium text-sm"
                            >
                              {Math.floor(estimationsProject.estimations.estimatedCostTraditional - estimationsProject.estimatedCost.tokens).toLocaleString('en-US').replace(/,/g, ',')}
                            </motion.span>
                          </AnimatePresence>
                        </div>

                        <div className="flex items-center gap-0.5 mt-0.5">
                          <div className="w-2 h-2 rounded-full bg-white/10 flex items-center justify-center">
                            <div className="w-1 h-1 rounded-full bg-orange-400"></div>
                          </div>
                          <span className="text-white/40 text-[9px] font-light">{estimationsProject.estimations.estimatedTimeTraditional - Math.floor(estimationsProject.timeMetrics.actual / 3600)}h</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div
                  className={`hidden justify-between cursor-pointer z-10 flex items-center ${isLoggedIn() && 'bg-[#1F29377F] hover:bg-[#3741517F] rounded-xl p-2 min-[840px]:pr-4'}  transition-all duration-200`}
                >
                  <div className="bg-black/20 absolute opacity-0 pointer-events-none border rounded-lg border-transparent">
                    <LanguageSelector hideText={true} />
                  </div>
                  {handleSendMessage && (
                    <div ref={settingsRef}>
                      {isLoggedIn() ? (
                        <div onClick={handleUserIconClick} className="rounded-lg w-max">
                          <div className="aligned-menu-header">
                            <div className="flex items-center lg:gap-4">
                              <div className="relative">
                                <img
                                  crossOrigin="anonymous"
                                  src={memoizedAvatarUrl}
                                  alt="Profile"
                                  className="w-10 h-10 rounded-full object-cover border-2 border-yellow-400"
                                />
                                {/*<p*/}
                                {/*  className="absolute -bottom-1 -right-1 px-2 py-0.5 rounded-full bg-yellow-400 text-center text-white text-[12px]">*/}
                                {/*  1*/}
                                {/*</p>*/}
                              </div>
                              <div className="flex flex-col items-start">
                                <p className="text-sm md:text-base font-light text-white uppercase font-manrope hidden lg:block">
                                  {userData?.username}
                                </p>
                                {userDataFromAffiliate && (
                                  <div className="flex items-center gap-2 text-xs py-[2px] ">
                                    {userDataFromAffiliate?.teamName && (
                                      <>
                                      <span className="text-[#60A5FA] text-[14px] font-manrope hidden lg:flex gap-2">
                                        <FaUserGroup />
                                        {userDataFromAffiliate.teamName}
                                      </span>
                                        <span className="text-gray-500 hidden lg:block">•</span>
                                      </>
                                    )}
                                    <span className="text-yellow-400 text-[14px] font-manrope hidden lg:flex gap-2">
                                    <FaStar />
                                      {userDataFromAffiliate?.stars?.toLocaleString()}
                                  </span>
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              <div className="text-[#4ADE80] text-xs sm:text-sm">
                                <TokenRemaining isStreaming={isStreaming || false} isHeader />
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : isDashboardPage || isSettingsPage ? (
                        <img
                          className="bg-[#BEDDFF] w-[48px] h-[48px] rounded-full cursor-pointer"
                          src={userImg}
                          onClick={() => setShowSettings(true)}
                          alt="User avatar"
                        />
                      ) : (
                        <>
                          <div style={{ width: 'max-content' }} className={'flex items-center gap-3'}>
                            <NavLink to="/login" className={'max-lg:hidden'}>
                              <button className="text-sm px-3 py-1.5 rounded-md relative text-[#010101] bg-[#49de80] flex gap-2 items-center">
                                <img src={UserIconDark} className="min-w-[18px]" alt="User icon" />
                                <span className="text-[13px] font-normal tracking-[0.4px] text-nowrap">Login</span>
                              </button>
                            </NavLink>
                            <div className="bg-black/20 border rounded-lg border-transparent">
                              <LanguageSelector hideText={true} />
                            </div>
                            <NavLink to="/login" className="lg:hidden">
                              <button className="w-10 h-10 cursor-pointer shadow-sm ring-2 ring-[#49de80] flex items-center justify-center text-black p-2 bg-[#49de80] rounded-full glass-button">
                                <img src={UserIconDark} className="w-6 h-6 " alt="User icon" />
                              </button>
                            </NavLink>
                          </div>
                        </>
                      )}
                      {showSettings && (
                        <div
                          style={{ overflow: 'visible' }}
                          className={`${
                            chat.started ? 'user-list-chat-open' : '!bg-[#0a1730b3]'
                          } z-[9999] bg-[#0a1730b3] absolute top-[63px] backdrop-blur-sm px-3 w-[320px]  right-0 rounded-xl shadow-lg border border-gray-800 overflow-hidden px-[20px] py-[8px]`}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <div>
                            <u className="no-underline no-list-style">
                              <div className={'px-[20px] py-[8px] border-b-[0.3px] border-[#9FA5B566] lg:hidden block'}>
                                <p className="text-sm md:text-base font-light text-white uppercase font-manrope ">
                                  {userData?.username}
                                </p>
                              </div>
                              <ClientOnly>
                                {() =>
                                  showSettings &&
                                  ListsItems.map((item, index) => {
                                    return (
                                      <ListItem
                                        key={index}
                                        title={item.title}
                                        icon={item.icon}
                                        description={item.description}
                                        path={item.path}
                                        isExternal={item.isExternal}
                                        openInNewTab={chatStarted}
                                        onClick={item.onClick}
                                      />
                                    );
                                  })
                                }
                              </ClientOnly>

                              <div className="border border-transparent py-3 sm:p-3 cursor-pointer relative rounded-lg flex hover:bg-[#1F293780] items-center w-full text-black dark:text-white mt-[4px] group">
                                <LanguageSelector />
                              </div>
                            </u>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-px bg-white/5" />
      </header>
      <LogoutConfirmModal open={showLogoutModal} onConfirm={handleLogoutConfirm} onCancel={handleLogoutCancel} />
    </TooltipProvider>
  );
}
