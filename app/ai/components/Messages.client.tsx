import type { Message } from 'ai';
import React, { Fragment, use, useEffect, useMemo, useRef, useState } from 'react';
import { classNames } from '~/utils/classNames';
import { AssistantMessage } from './AssistantMessage';
import { UserMessage } from './UserMessage';
import { useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import '../../components/styles/index.scss?url';
import WithTooltip from '~/ai/components/Tooltip';
import userImg from '/images/user3.svg?url';
import { Copy } from 'lucide-react';
import copy from 'copy-to-clipboard';
import ConfirmationDialog, { DialogConfirmationType } from '~/ai/components/ConfirmationDialog';
import { chatStore, setCheckingList, setCleaningProject } from '~/ai/lib/stores/chat';
import { useStore } from '@nanostores/react';
import { useUser } from '~/ai/lib/context/userContext';
import { backendApiFetch } from '~/ai/lib/backend-api';
import ScrollToBottom, { ScrollToBottomRef } from './ScrollToBottom';
import { sessionStore } from '../lib/stores/session/sessionStore';
import { debounce } from '~/utils/debounce';
interface MessagesProps {
  id?: string;
  className?: string;
  isStreaming?: boolean;
  messages?: Message[];
  isTyping?: boolean;
}

export const Messages = React.forwardRef<HTMLDivElement, MessagesProps>((props: MessagesProps, ref) => {
  const { id, isStreaming = false, messages = [] } = props;
  const location = useLocation();
  const { checkingList, cleaningProject } = useStore(chatStore);
  const { user } = useUser();
  const [userDataFromAffiliate, setUserDataFromAffiliate] = useState<
    | undefined
    | {
      avatar: string;
    }
  >(undefined);

  useEffect(() => {
    const fetchUserDataAffiliate = async () => {
      try {
        const response = await backendApiFetch(`/affiliate/user-affiliate-information`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response?.ok) {
          const data: any = await response.json();
          setUserDataFromAffiliate(data);
        }
      } catch (error) {
        console.error('Error fetching user data from affiliate:', error);
      }
    };

    fetchUserDataAffiliate();
  }, []);

  const handleRewind = (messageId: string) => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.set('rewindTo', messageId);
    window.location.search = searchParams.toString();
  };

  const [dialogContent, setDialogContent] = useState<{
    type: DialogConfirmationType.ROLLBACK;
    item: { description: string; id: string };
  } | null>(null);

  const closeDialog = () => {
    setDialogContent(null);
  };

  useEffect(() => {
    if (checkingList && !isStreaming) {
      setCheckingList(false);
    }

    if (cleaningProject && !isStreaming) {
      setCleaningProject(false);
    }
  }, [isStreaming]);

  if (messages.length && !isStreaming) {
    messages.forEach((item) => {
      (item.annotations as Array<{ value: { processed: boolean; totalTokens: number } }>)?.forEach((annotation) => {
        if (!annotation.value?.processed && annotation.value?.totalTokens) {
          annotation.value.processed = true;
        }
      });
    });
  }

  const memoizedAvatarUrl = useMemo(() => {
    if (typeof user?.profilePicture === 'string' && user?.profilePicture) {
      return user.profilePicture;
    }

    if (userDataFromAffiliate?.avatar) {
      return userDataFromAffiliate.avatar;
    }

    return userImg;
  }, [user?.profilePicture, userDataFromAffiliate?.avatar]);

  const scrollRef = useRef<ScrollToBottomRef>(null);

  useEffect(() => {
    const unsubscribers = [
      sessionStore.onAction.subscribe(debounce(() => scrollRef.current?.scrollToBottom({ onlyOnHover: false }), 300)),
      sessionStore.onStreamEnd.listen(() => setTimeout(() => scrollRef.current?.scrollToBottom({ onlyOnHover: false }), 100)),
      sessionStore.onStreamStart.listen(() => setTimeout(() => scrollRef.current?.scrollToBottom({ onlyOnHover: false }), 100)),
    ];
    return () => unsubscribers.forEach(unsubscribe => unsubscribe());
  }, []);



  return (
    <div id={id} ref={ref} className={classNames(props.className, 'flex flex-col', 'overflow-y-auto')}>
      <ScrollToBottom ref={scrollRef} className="flex-1 flex-messages">
        {messages.length > 0
          ? messages.map((message, index) => {
            const { role, content, id: messageId, annotations, data, reasoning, experimental_attachments } = message;
            const isUserMessage = role === 'user';
            const isHidden =
              annotations?.includes('hidden') ||
              (data as { annotations?: string[] })?.annotations?.includes('hidden');

            if (isHidden) {
              return <Fragment key={index} />;
            }

            return (
              <div key={`message-${index}`} className="pl-4">
                <div className={classNames('flex gap-4 w-full rounded-[calc(0.75rem-1px)] bg-chat-biela')}>
                  <div className={`special-bg ${isUserMessage ? 'user-message' : ''}`}>
                    <div className="w-full justify-between items-start flex-my-message">
                      <div className="flex items-start items-center gap-3 px-4 py-3 main-top-flex">
                        {isUserMessage && !(data as { hideUserImage?: boolean })?.hideUserImage && (
                          <div className="flex items-center justify-center w-[34px] h-[34px] overflow-hidden text-gray-600 rounded-full shrink-0 self-start">
                            <img
                              crossOrigin="anonymous"
                              src={memoizedAvatarUrl}
                              onError={(e) => (e.currentTarget.src = userImg)}
                              className="w-[30px] h-[30px] rounded-full cursor-pointer"
                              alt="User"
                            />
                          </div>
                        )}

                        <div className="manrope grid grid-col-1 w-full relative">
                          {isUserMessage ? (
                            <UserMessage content={content} experimental_attachments={experimental_attachments} />
                          ) : (
                            <AssistantMessage
                              content={content}
                              annotations={message.annotations}
                              reasoning={reasoning}
                            />
                          )}
                        </div>
                      </div>

                      <div className="rounded-b-md max-h-[41px] border border-white/5 bg-[#0A0F1C] border-white/5 config-btn-container flex items-center justify-end gap-2 p-2 copy-border">
                        {messageId && (
                          <WithTooltip tooltip="Copy this message">
                            <button
                              className={
                                'configuration-btn px-2 py-1 rounded text-xs text-white/50 hover:bg-white/5 transition-colors flex items-center gap-1.5'
                              }
                              onClick={() => {
                                const MessageContent = Array.isArray(content)
                                  ? content
                                    .filter((item) => item.type === 'text')
                                    .map((item) => item.text)
                                    .join('\n\n')
                                  : content;

                                const Message = MessageContent.replace(
                                  /<div class="__bielaArtifact__"[^>]*><\/div>/g,
                                  '',
                                );

                                const copySuccess = copy(Message);

                                if (copySuccess) {
                                  toast.success('Copied to clipboard', { pauseOnFocusLoss: false });
                                } else {
                                  toast.error('Failed to copy to clipboard', {
                                    pauseOnFocusLoss: false,
                                  });
                                }
                              }}
                              key="i-ph:arrow-u-up-left"
                            >
                              <Copy
                                size={10}
                                color="#fff"
                                style={{
                                  filter:
                                    'brightness(0) saturate(100%) invert(58%) sepia(7%) saturate(303%) hue-rotate(177deg) brightness(90%) contrast(89%)',
                                  width: '14px',
                                  height: '14px',
                                }}
                              />
                              <span>Copy</span>
                            </button>
                          </WithTooltip>
                        )}
                      </div>
                    </div>
                    {/* end of .flex.w-full.justify-between */}
                  </div>
                  {/* end of .special-bg */}
                </div>
              </div>
            );
          })
          : null}
        {isStreaming && messages.length > 0 && messages[messages.length - 1].role === 'assistant' && (
          <div
            className="text-center w-full text-biela-elements-textSecondary i-svg-spinners:3-dots-fade text-4xl mt-4"></div>
        )}
      </ScrollToBottom>
      <ConfirmationDialog
        isOpen={dialogContent !== null}
        type={dialogContent?.type ?? DialogConfirmationType.ROLLBACK}
        description={dialogContent?.item.description ?? ''}
        onConfirm={() => {
          if (dialogContent?.type === DialogConfirmationType.ROLLBACK) {
            handleRewind(dialogContent.item.id);
          }

          closeDialog();
        }}
        onCancel={closeDialog}
      />
    </div>
  );
});
